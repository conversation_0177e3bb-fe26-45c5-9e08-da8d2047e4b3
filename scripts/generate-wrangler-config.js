#!/usr/bin/env node

/**
 * 动态生成 wrangler.json 配置文件
 * 从 .env 文件读取 KV 配置
 */

const fs = require('fs');
const path = require('path');

// 读取 .env 文件
function loadEnv() {
  const envPath = path.join(__dirname, '..', '.env');
  if (!fs.existsSync(envPath)) {
    console.error('❌ .env 文件不存在');
    process.exit(1);
  }

  const envContent = fs.readFileSync(envPath, 'utf8');
  const env = {};
  
  envContent.split('\n').forEach(line => {
    line = line.trim();
    if (line && !line.startsWith('#')) {
      const [key, ...valueParts] = line.split('=');
      if (key && valueParts.length > 0) {
        let value = valueParts.join('=').trim();
        // 移除引号
        if ((value.startsWith('"') && value.endsWith('"')) || 
            (value.startsWith("'") && value.endsWith("'"))) {
          value = value.slice(1, -1);
        }
        env[key] = value;
      }
    }
  });

  return env;
}

// 生成 wrangler.json 配置
function generateWranglerConfig(env) {
  const kvId = env.CLOUDFLARE_KV_SHORTLINKS_ID;
  const kvBinding = env.CLOUDFLARE_KV_SHORTLINKS_BINDING || 'SHORTLINKS';

  if (!kvId) {
    console.error('❌ 缺少 CLOUDFLARE_KV_SHORTLINKS_ID 环境变量');
    process.exit(1);
  }

  const config = {
    "$schema": "node_modules/wrangler/config-schema.json",
    "name": "shortlink",
    "main": ".open-next/worker.js",
    "compatibility_date": "2025-04-01",
    "compatibility_flags": [
      "nodejs_compat"
    ],
    "minify": true,
    "assets": {
      "directory": ".open-next/assets",
      "binding": "ASSETS"
    },
    "observability": {
      "enabled": true
    },
    "upload_source_maps": true,
    "kv_namespaces": [
      {
        "binding": kvBinding,
        "id": kvId
      }
    ]
  };

  return config;
}

// 主函数
function main() {
  try {
    console.log('🔧 正在生成 wrangler.json 配置...');
    
    const env = loadEnv();
    const config = generateWranglerConfig(env);
    
    const configPath = path.join(__dirname, '..', 'wrangler.json');
    fs.writeFileSync(configPath, JSON.stringify(config, null, 2));
    
    console.log('✅ wrangler.json 配置已生成');
    console.log(`📋 KV Namespace: ${config.kv_namespaces[0].binding} (${config.kv_namespaces[0].id})`);
  } catch (error) {
    console.error('❌ 生成配置失败:', error.message);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { loadEnv, generateWranglerConfig };
