# 短链接服务 (Shortlink Service)

基于 Next.js 和 Cloudflare Workers 构建的短链接服务，支持链接缩短、访问统计和管理功能。

## 功能特性

- 🔗 **链接缩短**: 将长链接转换为短链接
- 📊 **访问统计**: 记录每个短链接的访问次数
- 🛡️ **管理后台**: 支持密码保护的管理界面
- 🚀 **高性能**: 部署在 Cloudflare Workers 上，全球边缘计算
- 💾 **数据持久化**: 使用 Cloudflare KV 存储数据
- 🔧 **环境配置**: 支持通过环境变量灵活配置

## 快速开始

### 1. 环境配置

复制环境变量模板并配置：

```bash
cp .env.example .env
```

编辑 `.env` 文件：

```bash
NEXT_PUBLIC_BASE_URL='https://your-domain.com'
ADMIN_PASSWORD='your-admin-password'
CLOUDFLARE_KV_SHORTLINKS_ID='your-kv-namespace-id'
CLOUDFLARE_KV_SHORTLINKS_BINDING='SHORTLINKS'
```

### 2. 创建 Cloudflare KV Namespace

在 Cloudflare Dashboard 中创建一个 KV Namespace，并将 ID 填入 `.env` 文件。

### 3. 安装依赖

```bash
npm install
```

### 4. 生成配置文件

```bash
npm run config:generate
```

### 5. 开发模式

```bash
npm run dev
```

访问 [http://localhost:3000](http://localhost:3000) 查看应用。

## 使用说明

### 创建短链接

发送 POST 请求到 `/api/shorten`：

```bash
curl -X POST http://localhost:3000/api/shorten \
  -H "Content-Type: application/json" \
  -d '{"url": "https://example.com"}'
```

### 管理后台

访问 `/admin/login` 使用管理员密码登录，然后可以：

- 查看所有短链接
- 搜索和筛选链接
- 批量删除链接
- 查看访问统计

## 部署到生产环境

| 命令                      | 说明                              |
| :------------------------ | :-------------------------------- |
| `npm run config:generate` | 生成 wrangler.json 配置文件       |
| `npm run build`           | 构建生产版本                      |
| `npm run preview`         | 本地预览构建结果                  |
| `npm run check`           | 检查构建和类型，执行 dry-run 部署 |
| `npm run deploy`          | 部署到 Cloudflare Workers         |
| `npm run cf-typegen`      | 生成 TypeScript 类型定义          |

## KV 配置管理

本项目支持通过环境变量管理 Cloudflare KV 配置。详细说明请参考 [KV 配置文档](docs/KV_CONFIGURATION.md)。

### 主要特性

- 🔧 **环境变量配置**: 通过 `.env` 文件管理 KV Namespace ID
- 🔄 **自动配置生成**: 根据环境变量动态生成 `wrangler.json`
- 🌍 **多环境支持**: 支持开发、测试、生产环境分离
- 🔒 **安全管理**: 避免在代码中硬编码敏感信息

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js/) - your feedback and contributions are welcome!
