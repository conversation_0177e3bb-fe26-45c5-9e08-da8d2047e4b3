"use client";
import { useState } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";

export default function Home() {
  const [url, setUrl] = useState("");
  const [shortUrl, setShortUrl] = useState("");
  const [loading, setLoading] = useState(false);

  async function handleSubmit(e: React.FormEvent<HTMLFormElement>) {
    e.preventDefault();
    setLoading(true);
    setShortUrl("");
    try {
      const res = await fetch("/api/shorten", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ url }),
      });
      const data: { shortUrl?: string; error?: string } = await res.json();
      if (data.shortUrl) {
        setShortUrl(data.shortUrl);
        toast.success("短链生成成功！");
      } else {
        toast.error(data.error || "生成失败");
      }
    } catch {
      toast.error("网络错误");
    } finally {
      setLoading(false);
    }
  }

  function handleCopy() {
    if (shortUrl) {
      navigator.clipboard.writeText(shortUrl);
      toast.success("已复制到剪贴板");
    }
  }

  return (
    <main className="flex min-h-screen flex-col items-center justify-center p-4">
      <h1 className="text-3xl font-bold mb-6">短链接生成器</h1>
      <form onSubmit={handleSubmit} className="flex flex-col gap-4 w-full max-w-md">
        <Input
          placeholder="请输入原始链接，如 https://example.com"
          value={url}
          onChange={e => setUrl(e.target.value)}
          required
        />
        <Button type="submit" disabled={loading}>
          {loading ? "生成中..." : "生成短链接"}
        </Button>
      </form>
      {shortUrl && (
        <div className="mt-6 flex flex-col items-center gap-2">
          <span className="text-lg font-medium">短链：</span>
          <div className="flex items-center gap-2">
            <a href={shortUrl} target="_blank" rel="noopener noreferrer" className="text-blue-600 underline">{shortUrl}</a>
            <Button variant="outline" size="sm" onClick={handleCopy}>复制</Button>
          </div>
        </div>
      )}
    </main>
  );
}
