import { NextRequest, NextResponse } from "next/server";
import { nanoid } from "nanoid";
import { setShortlink, addToIndex, ShortlinkData } from "../_kv";

const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL || "http://localhost:3000";

// 判断是否在 Cloudflare Workers 环境
function isCFWorkerEnv(env: any): env is { SHORTLINKS: KVNamespace } {
  return (
    !!env &&
    typeof env.SHORTLINKS === "object" &&
    typeof env.SHORTLINKS.get === "function"
  );
}

export async function POST(req: NextRequest) {
  try {
    const body: { url?: string } = await req.json();
    const url = body.url;
    if (!url || typeof url !== "string") {
      return NextResponse.json({ error: "Invalid url" }, { status: 400 });
    }
    // 生成短链ID
    const shortId = nanoid(7);
    const data: ShortlinkData = {
      url,
      createdAt: Date.now(),
      visitCount: 0,
    };
    const env = {
      SHORTLINKS: process.env.SHORTLINKS,
    };
    await setShortlink(env, shortId, data);
    await addToIndex(env, shortId);
    return NextResponse.json({
      shortId,
      shortUrl: `${BASE_URL}/${shortId}`,
    });
  } catch (e) {
    return NextResponse.json({ error: "Invalid request" }, { status: 400 });
  }
}
