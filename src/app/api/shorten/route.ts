import { NextRequest, NextResponse } from "next/server";
import { nanoid } from "nanoid";
import { setShortlink, addToIndex, ShortlinkData } from "../_kv";

const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL || "http://localhost:3000";

export async function POST(req: NextRequest) {
  try {
    const body: { url?: string } = await req.json();
    const url = body.url;
    if (!url || typeof url !== "string") {
      return NextResponse.json({ error: "Invalid url" }, { status: 400 });
    }
    // 生成短链ID
    const shortId = nanoid(7);
    const data: ShortlinkData = {
      url,
      createdAt: Date.now(),
      visitCount: 0,
    };

    await setShortlink(shortId, data);
    await addToIndex(shortId);
    return NextResponse.json({
      shortId,
      shortUrl: `${BASE_URL}/${shortId}`,
    });
  } catch (e) {
    return NextResponse.json({ error: "Invalid request" }, { status: 400 });
  }
}
