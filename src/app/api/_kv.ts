import { NextRequest } from "next/server";

export const INDEX_KEY = "shortlink:index";
export const ADMIN_COOKIE = "admin_auth";
export const ADMIN_PASSWORD = process.env.ADMIN_PASSWORD || "admin123";

export type ShortlinkData = {
  url: string;
  createdAt: number;
  visitCount: number;
};

export type KVNamespace = {
  get: (key: string) => Promise<string | null>;
  put: (key: string, value: string) => Promise<void>;
  delete: (key: string) => Promise<void>;
};

// 判断是否在 Cloudflare Workers 环境
export function isCFWorkerEnv(env: any): env is { SHORTLINKS: KVNamespace } {
  return (
    !!env &&
    typeof env.SHORTLINKS === "object" &&
    typeof env.SHORTLINKS.get === "function"
  );
}

// 获取 Cloudflare 环境
export function getCloudflareEnv(): { SHORTLINKS: KVNamespace } | null {
  try {
    // 使用 OpenNext Cloudflare 的官方方法获取环境
    const { getCloudflareContext } = require("@opennextjs/cloudflare");
    const { env } = getCloudflareContext();
    if (env && env.SHORTLINKS) {
      return env;
    }
  } catch (error) {
    // 如果不在 Cloudflare Workers 环境中，getCloudflareContext 会抛出错误
    console.log(
      "Not in Cloudflare Workers environment:",
      error instanceof Error ? error.message : String(error)
    );
  }

  return null;
}

// 全局 mock KV
// eslint-disable-next-line no-var
declare global {
  var kvMock: Map<string, string> | undefined;
  var kvIndex: Set<string> | undefined;
}
export const kvMock = globalThis.kvMock || new Map<string, string>();
if (!globalThis.kvMock) globalThis.kvMock = kvMock;
export const kvIndex = globalThis.kvIndex || new Set<string>();
if (!globalThis.kvIndex) globalThis.kvIndex = kvIndex;

// 权限校验
export function isAuthed(req: NextRequest) {
  const cookie = req.cookies.get(ADMIN_COOKIE)?.value;
  return cookie === ADMIN_PASSWORD;
}

// 存储短链数据
export async function setShortlink(shortId: string, data: ShortlinkData) {
  const env = getCloudflareEnv();
  if (env) {
    await env.SHORTLINKS.put(shortId, JSON.stringify(data));
  } else {
    kvMock.set(shortId, JSON.stringify(data));
  }
}

// 获取短链数据
export async function getShortlink(
  shortId: string
): Promise<ShortlinkData | null> {
  const env = getCloudflareEnv();
  if (env) {
    const raw = await env.SHORTLINKS.get(shortId);
    return raw ? JSON.parse(raw) : null;
  } else {
    const raw = kvMock.get(shortId);
    return raw ? JSON.parse(raw) : null;
  }
}

// 删除短链数据
export async function deleteShortlink(shortId: string) {
  const env = getCloudflareEnv();
  if (env) {
    await env.SHORTLINKS.delete(shortId);
  } else {
    kvMock.delete(shortId);
  }
}

// 获取索引
export async function getIndex(): Promise<string[]> {
  const env = getCloudflareEnv();
  if (env) {
    const indexRaw = await env.SHORTLINKS.get(INDEX_KEY);
    return indexRaw ? JSON.parse(indexRaw) : [];
  } else {
    return Array.from(kvIndex);
  }
}

// 添加到索引
export async function addToIndex(shortId: string) {
  const env = getCloudflareEnv();
  if (env) {
    const indexRaw = await env.SHORTLINKS.get(INDEX_KEY);
    let index: string[] = indexRaw ? JSON.parse(indexRaw) : [];
    index.push(shortId);
    await env.SHORTLINKS.put(INDEX_KEY, JSON.stringify(index));
  } else {
    kvIndex.add(shortId);
  }
}

// 从索引移除
export async function removeFromIndex(shortId: string) {
  const env = getCloudflareEnv();
  if (env) {
    const indexRaw = await env.SHORTLINKS.get(INDEX_KEY);
    let index: string[] = indexRaw ? JSON.parse(indexRaw) : [];
    index = index.filter((id) => id !== shortId);
    await env.SHORTLINKS.put(INDEX_KEY, JSON.stringify(index));
  } else {
    kvIndex.delete(shortId);
  }
}

// 获取短链数据并增加访问次数
export async function getShortlinkAndIncrement(
  shortId: string
): Promise<ShortlinkData | null> {
  const env = getCloudflareEnv();
  if (env) {
    const raw = await env.SHORTLINKS.get(shortId);
    if (raw) {
      const data = JSON.parse(raw) as ShortlinkData;
      data.visitCount = (data.visitCount || 0) + 1;
      await env.SHORTLINKS.put(shortId, JSON.stringify(data));
      return data;
    }
    return null;
  } else {
    const raw = kvMock.get(shortId);
    if (raw) {
      const data = JSON.parse(raw) as ShortlinkData;
      data.visitCount = (data.visitCount || 0) + 1;
      kvMock.set(shortId, JSON.stringify(data));
      return data;
    }
    return null;
  }
}
