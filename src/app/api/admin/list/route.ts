import { NextRequest, NextResponse } from "next/server";
import { getIndex, isAuthed, isCFWorkerEnv, kvMock } from "../../_kv";

export async function GET(req: NextRequest) {
  if (!isAuthed(req)) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }
  // 分页和筛选参数
  const { searchParams } = new URL(req.url);
  const page = parseInt(searchParams.get("page") || "1", 10);
  const pageSize = parseInt(searchParams.get("pageSize") || "10", 10);
  const q = (searchParams.get("q") || "").toLowerCase();
  const minVisit = searchParams.get("minVisit")
    ? parseInt(searchParams.get("minVisit")!, 10)
    : undefined;
  const maxVisit = searchParams.get("maxVisit")
    ? parseInt(searchParams.get("maxVisit")!, 10)
    : undefined;
  const env = {
    SHORTLINKS: process.env.SHORTLINKS,
  };
  let allIds: string[] = await getIndex(env);
  // 先查出所有数据
  let allLinks: any[] = [];
  if (isCFWorkerEnv(env)) {
    for (const id of allIds) {
      const raw = await env.SHORTLINKS.get(id);
      if (raw) {
        allLinks.push({ id, ...JSON.parse(raw) });
      }
    }
  } else {
    allLinks = allIds
      .map((id) => {
        const raw = kvMock.get(id);
        return raw ? { id, ...JSON.parse(raw) } : null;
      })
      .filter(Boolean);
  }
  // 搜索和筛选
  if (q) {
    allLinks = allLinks.filter(
      (l) => l.id.toLowerCase().includes(q) || l.url.toLowerCase().includes(q)
    );
  }
  if (typeof minVisit === "number") {
    allLinks = allLinks.filter((l) => l.visitCount >= minVisit);
  }
  if (typeof maxVisit === "number") {
    allLinks = allLinks.filter((l) => l.visitCount <= maxVisit);
  }
  const total = allLinks.length;
  const start = (page - 1) * pageSize;
  const end = start + pageSize;
  const result = allLinks.slice(start, end);
  return NextResponse.json({ links: result, total });
}
