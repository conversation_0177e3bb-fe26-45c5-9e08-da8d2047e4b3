import { NextRequest, NextResponse } from "next/server";
import {
  isAuthed,
  isCFWorkerEnv,
  removeFromIndex,
  deleteShortlink,
  kvMock,
  kvIndex,
} from "../../_kv";

export async function DELETE(
  req: NextRequest,
  { params }: { params: { shortId: string } }
) {
  if (!isAuthed(req)) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }
  const shortId = params.shortId;
  const env = {
    SHORTLINKS: process.env.SHORTLINKS,
  };
  if (isCFWorkerEnv(env)) {
    await deleteShortlink(env, shortId);
    await removeFromIndex(env, shortId);
    return NextResponse.json({ ok: true });
  } else {
    kvMock.delete(shortId);
    kvIndex.delete(shortId);
    return NextResponse.json({ ok: true });
  }
}
