import { NextRequest, NextResponse } from "next/server";
import { isAuthed, removeFromIndex, deleteShortlink } from "../../_kv";

export async function DELETE(
  req: NextRequest,
  { params }: { params: { shortId: string } }
) {
  if (!isAuthed(req)) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }
  const shortId = params.shortId;

  await deleteShortlink(shortId);
  await removeFromIndex(shortId);
  return NextResponse.json({ ok: true });
}
