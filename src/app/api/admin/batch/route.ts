import { NextRequest, NextResponse } from "next/server";
import {
  isAuthed,
  isCFWorkerEnv,
  getIndex,
  deleteShortlink,
  kvMock,
  kvIndex,
} from "../../_kv";

export async function DELETE(req: NextRequest) {
  if (!isAuthed(req)) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }
  const body: { ids?: string[] } = await req.json();
  const ids = body.ids;
  if (!Array.isArray(ids)) {
    return NextResponse.json({ error: "Invalid ids" }, { status: 400 });
  }
  const env = {
    SHORTLINKS: process.env.SHORTLINKS,
  };
  if (isCFWorkerEnv(env)) {
    let index: string[] = await getIndex(env);
    for (const id of ids) {
      await deleteShortlink(env, id);
      index = index.filter((i) => i !== id);
    }
    // 更新索引
    await env.SHORTLINKS.put("shortlink:index", JSON.stringify(index));
    return NextResponse.json({ ok: true });
  } else {
    for (const id of ids) {
      kvMock.delete(id);
      kvIndex.delete(id);
    }
    return NextResponse.json({ ok: true });
  }
}
