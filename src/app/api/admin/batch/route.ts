import { NextRequest, NextResponse } from "next/server";
import { isAuthed, deleteShortlink, removeFromIndex } from "../../_kv";

export async function DELETE(req: NextRequest) {
  if (!isAuthed(req)) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }
  const body: { ids?: string[] } = await req.json();
  const ids = body.ids;
  if (!Array.isArray(ids)) {
    return NextResponse.json({ error: "Invalid ids" }, { status: 400 });
  }

  for (const id of ids) {
    await deleteShortlink(id);
    await removeFromIndex(id);
  }
  return NextResponse.json({ ok: true });
}
