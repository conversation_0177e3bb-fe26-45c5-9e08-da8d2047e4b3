"use client";
import { useState } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { useRouter } from "next/navigation";

const ADMIN_COOKIE = "admin_auth";

export default function AdminLogin() {
  const [password, setPassword] = useState("");
  const [error, setError] = useState("");
  const router = useRouter();

  async function handleSubmit(e: React.FormEvent<HTMLFormElement>) {
    e.preventDefault();
    setError("");
    // 直接设置cookie，实际生产建议用API登录
    document.cookie = `${ADMIN_COOKIE}=${password}; path=/`;
    // 简单跳转，后端API会校验cookie
    router.push("/admin");
  }

  return (
    <main className="flex min-h-screen flex-col items-center justify-center p-4">
      <h1 className="text-2xl font-bold mb-6">后台登录</h1>
      <form onSubmit={handleSubmit} className="flex flex-col gap-4 w-full max-w-xs">
        <Input
          type="password"
          placeholder="请输入后台密码"
          value={password}
          onChange={e => setPassword(e.target.value)}
          required
        />
        <Button type="submit">登录</Button>
        {error && <div className="text-red-500 text-sm">{error}</div>}
      </form>
    </main>
  );
} 