"use client";
import { useEffect, useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useRouter } from "next/navigation";

interface Shortlink {
  id: string;
  url: string;
  createdAt: number;
  visitCount: number;
}

const PAGE_SIZE_OPTIONS = [10, 20, 30, 50];

export default function AdminPage() {
  const [links, setLinks] = useState<Shortlink[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [total, setTotal] = useState(0);
  const [selected, setSelected] = useState<string[]>([]);
  const [q, setQ] = useState("");
  const [minVisit, setMinVisit] = useState("");
  const [maxVisit, setMaxVisit] = useState("");
  const router = useRouter();

  async function fetchLinks(page = 1, pageSize = 10, q = "", minVisit = "", maxVisit = "") {
    setLoading(true);
    setError("");
    const params = new URLSearchParams({ page: String(page), pageSize: String(pageSize) });
    if (q) params.append("q", q);
    if (minVisit) params.append("minVisit", minVisit);
    if (maxVisit) params.append("maxVisit", maxVisit);
    const res = await fetch(`/api/admin/list?${params.toString()}`);
    if (res.status === 401) {
      router.push("/admin/login");
      return;
    }
    const data: { links: Shortlink[]; total: number } = await res.json();
    setLinks(data.links || []);
    setTotal(data.total || 0);
    setLoading(false);
    setSelected([]);
  }

  useEffect(() => {
    fetchLinks(page, pageSize, q, minVisit, maxVisit);
    // eslint-disable-next-line
  }, [page, pageSize]);

  function handleSearch(e: React.FormEvent) {
    e.preventDefault();
    setPage(1);
    fetchLinks(1, pageSize, q, minVisit, maxVisit);
  }

  async function handleDelete(id: string) {
    if (!window.confirm("确定要删除该短链吗？")) return;
    const res = await fetch(`/api/admin/${id}`, { method: "DELETE" });
    if (res.status === 401) {
      router.push("/admin/login");
      return;
    }
    fetchLinks(page, pageSize, q, minVisit, maxVisit);
  }

  async function handleBatchDelete() {
    if (selected.length === 0) return;
    if (!window.confirm(`确定要删除选中的${selected.length}个短链吗？`)) return;
    const res = await fetch(`/api/admin/batch`, {
      method: "DELETE",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ ids: selected }),
    });
    if (res.status === 401) {
      router.push("/admin/login");
      return;
    }
    fetchLinks(page, pageSize, q, minVisit, maxVisit);
  }

  function handleSelectAll(e: React.ChangeEvent<HTMLInputElement>) {
    if (e.target.checked) {
      setSelected(links.map(l => l.id));
    } else {
      setSelected([]);
    }
  }

  function handleSelect(id: string, checked: boolean) {
    setSelected(prev => checked ? [...prev, id] : prev.filter(i => i !== id));
  }

  const totalPages = Math.ceil(total / pageSize);

  return (
    <main className="flex min-h-screen flex-col items-center p-4">
      <h1 className="text-2xl font-bold mb-6">短链后台管理</h1>
      <form onSubmit={handleSearch} className="flex flex-wrap gap-2 mb-4 w-full max-w-2xl items-center">
        <Input
          className="w-48"
          placeholder="搜索短链ID或原始链接"
          value={q}
          onChange={e => setQ(e.target.value)}
        />
        <Input
          className="w-28"
          type="number"
          min={0}
          placeholder="最小访问次数"
          value={minVisit}
          onChange={e => setMinVisit(e.target.value)}
        />
        <Input
          className="w-28"
          type="number"
          min={0}
          placeholder="最大访问次数"
          value={maxVisit}
          onChange={e => setMaxVisit(e.target.value)}
        />
        <Button type="submit">搜索/筛选</Button>
        <div className="ml-auto flex items-center gap-2">
          <span>每页</span>
          <select
            className="border rounded px-2 py-1"
            value={pageSize}
            onChange={e => { setPageSize(Number(e.target.value)); setPage(1); }}
          >
            {PAGE_SIZE_OPTIONS.map(opt => (
              <option key={opt} value={opt}>{opt}</option>
            ))}
          </select>
          <span>条</span>
        </div>
      </form>
      <div className="flex items-center gap-4 mb-4 w-full max-w-2xl">
        <Button variant="destructive" size="sm" disabled={selected.length === 0} onClick={handleBatchDelete}>
          批量删除
        </Button>
        <span className="text-sm text-gray-500">已选 {selected.length} 项</span>
      </div>
      {loading ? (
        <div>加载中...</div>
      ) : error ? (
        <div className="text-red-500">{error}</div>
      ) : (
        <div className="w-full max-w-2xl">
          <table className="w-full border text-sm">
            <thead>
              <tr className="bg-gray-100 dark:bg-gray-800">
                <th className="p-2 border">
                  <input
                    type="checkbox"
                    checked={selected.length === links.length && links.length > 0}
                    onChange={handleSelectAll}
                  />
                </th>
                <th className="p-2 border">短链</th>
                <th className="p-2 border">原始链接</th>
                <th className="p-2 border">创建时间</th>
                <th className="p-2 border">访问次数</th>
                <th className="p-2 border">操作</th>
              </tr>
            </thead>
            <tbody>
              {links.map(link => (
                <tr key={link.id}>
                  <td className="p-2 border text-center">
                    <input
                      type="checkbox"
                      checked={selected.includes(link.id)}
                      onChange={e => handleSelect(link.id, e.target.checked)}
                    />
                  </td>
                  <td className="p-2 border">
                    <a href={`/${link.id}`} target="_blank" rel="noopener noreferrer" className="text-blue-600 underline">/{link.id}</a>
                  </td>
                  <td className="p-2 border max-w-xs truncate" title={link.url}>{link.url}</td>
                  <td className="p-2 border">{new Date(link.createdAt).toLocaleString()}</td>
                  <td className="p-2 border text-center">{link.visitCount}</td>
                  <td className="p-2 border text-center">
                    <Button variant="destructive" size="sm" onClick={() => handleDelete(link.id)}>删除</Button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
          <div className="flex items-center justify-between mt-4">
            <Button size="sm" disabled={page === 1} onClick={() => setPage(p => p - 1)}>上一页</Button>
            <span>第 {page} / {totalPages || 1} 页</span>
            <Button size="sm" disabled={page === totalPages || totalPages === 0} onClick={() => setPage(p => p + 1)}>下一页</Button>
          </div>
        </div>
      )}
    </main>
  );
} 