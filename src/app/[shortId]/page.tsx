import { redirect } from "next/navigation";

interface ShortIdPageProps {
  params: { shortId: string };
  // Cloudflare Workers 环境下会注入 env
  searchParams?: any;
}

// 判断是否在 Cloudflare Workers 环境
type KVNamespace = { get: (key: string) => Promise<string | null>; put: (key: string, value: string) => Promise<void> };
function getCFEnv(): { SHORTLINKS: KVNamespace } | null {
  // @ts-ignore
  if (typeof globalThis.env !== "undefined" && globalThis.env.SHORTLINKS) {
    // @ts-ignore
    return globalThis.env;
  }
  return null;
}

// 本地 mock KV
function getMockKV(): Map<string, string> {
  // eslint-disable-next-line no-var
  if (!(globalThis as any).kvMock) (globalThis as any).kvMock = new Map<string, string>();
  return (globalThis as any).kvMock;
}

export default async function ShortIdPage({ params }: ShortIdPageProps) {
  let url: string | undefined | null = undefined;
  const env = getCFEnv();
  const shortId = params.shortId;
  if (env) {
    const raw = await env.SHORTLINKS.get(shortId);
    if (raw) {
      const data = JSON.parse(raw) as { url: string; createdAt: number; visitCount: number };
      // 自增访问次数
      data.visitCount = (data.visitCount || 0) + 1;
      await env.SHORTLINKS.put(shortId, JSON.stringify(data));
      url = data.url;
    }
  } else {
    const kv = getMockKV();
    const raw = kv.get(shortId);
    if (raw) {
      const data = JSON.parse(raw) as { url: string; createdAt: number; visitCount: number };
      data.visitCount = (data.visitCount || 0) + 1;
      kv.set(shortId, JSON.stringify(data));
      url = data.url;
    }
  }
  if (url) {
    redirect(url);
  }
  return (
    <div className="flex flex-col items-center justify-center min-h-screen">
      <h1 className="text-2xl font-bold mb-4">404 Not Found</h1>
      <p>短链接不存在或已失效。</p>
    </div>
  );
} 