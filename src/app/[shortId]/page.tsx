import { redirect } from "next/navigation";
import { getShortlinkAndIncrement } from "../api/_kv";

interface ShortIdPageProps {
  params: { shortId: string };
}

export default async function ShortIdPage({ params }: ShortIdPageProps) {
  const shortId = params.shortId;
  const data = await getShortlinkAndIncrement(shortId);

  if (data && data.url) {
    redirect(data.url);
  }

  return (
    <div className="flex flex-col items-center justify-center min-h-screen">
      <h1 className="text-2xl font-bold mb-4">404 Not Found</h1>
      <p>短链接不存在或已失效。</p>
    </div>
  );
}
