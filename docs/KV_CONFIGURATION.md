# KV 配置管理

本项目支持通过环境变量来管理 Cloudflare KV 配置，使得在不同环境之间切换更加灵活和安全。

## 配置方式

### 1. 环境变量配置

在 `.env` 文件中设置以下变量：

```bash
# Cloudflare KV Configuration
CLOUDFLARE_KV_SHORTLINKS_ID='your-kv-namespace-id'
CLOUDFLARE_KV_SHORTLINKS_BINDING='SHORTLINKS'
```

### 2. 自动生成 wrangler.json

项目包含一个自动配置生成脚本，会根据 `.env` 文件中的设置动态生成 `wrangler.json` 配置。

## 使用方法

### 初始设置

1. 复制环境变量模板：
   ```bash
   cp .env.example .env
   ```

2. 编辑 `.env` 文件，填入你的 KV Namespace ID：
   ```bash
   CLOUDFLARE_KV_SHORTLINKS_ID='your-actual-kv-namespace-id'
   ```

3. 生成配置文件：
   ```bash
   npm run config:generate
   ```

### 开发和部署

所有相关的 npm 脚本都会自动生成配置：

```bash
# 生成类型定义
npm run cf-typegen

# 构建和检查
npm run check

# 部署到 Cloudflare
npm run deploy

# 本地预览
npm run preview
```

## 多环境支持

你可以为不同环境创建不同的 `.env` 文件：

- `.env.development` - 开发环境
- `.env.staging` - 测试环境  
- `.env.production` - 生产环境

然后在部署时指定对应的环境文件。

## 安全注意事项

1. **不要提交 `.env` 文件到代码仓库**
   - `.env` 文件已添加到 `.gitignore`
   - 只提交 `.env.example` 作为模板

2. **使用 Cloudflare Secrets 管理敏感信息**
   ```bash
   # 设置密钥
   wrangler secret put ADMIN_PASSWORD
   ```

3. **定期轮换 KV Namespace ID**
   - 在生产环境中定期更换 KV Namespace
   - 更新 `.env` 文件中的 ID

## 故障排除

### 配置生成失败

如果配置生成失败，检查：

1. `.env` 文件是否存在
2. `CLOUDFLARE_KV_SHORTLINKS_ID` 是否已设置
3. KV Namespace ID 格式是否正确

### 部署时找不到 KV Namespace

确保：

1. KV Namespace 已在 Cloudflare Dashboard 中创建
2. `.env` 文件中的 ID 与实际 Namespace ID 匹配
3. 运行 `npm run config:generate` 重新生成配置

## 脚本说明

- `npm run config:generate` - 根据 `.env` 生成 `wrangler.json`
- `scripts/generate-wrangler-config.js` - 配置生成脚本

这个系统让 KV 配置管理更加灵活和安全，支持多环境部署和团队协作。
