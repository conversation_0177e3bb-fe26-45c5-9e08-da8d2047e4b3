{"name": "next-starter-template", "description": "Build a full-stack web application with Next.js.", "cloudflare": {"label": "Next.js Framework Starter", "products": ["Workers"], "categories": [], "icon_urls": ["https://imagedelivery.net/wSMYJvS3Xw-n339CbDyDIA/5ca0ca32-e897-4699-d4c1-6b680512f000/public"], "preview_image_url": "https://imagedelivery.net/wSMYJvS3Xw-n339CbDyDIA/e42eec61-db86-49c8-7b29-c3ed4783e400/public", "publish": true}, "dependencies": {"@radix-ui/react-slot": "^1.2.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.514.0", "nanoid": "^5.1.5", "next": "14.2.26", "next-themes": "^0.4.6", "react": "18.3.1", "react-dom": "18.3.1", "sonner": "^2.0.5", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@opennextjs/cloudflare": "1.0.0-beta.3", "@types/node": "^22.15.19", "@types/react": "18.3.12", "@types/react-dom": "18.3.1", "eslint": "8.56.0", "eslint-config-next": "14.2.5", "postcss": "8.5.3", "tailwindcss": "3.4.17", "typescript": "5.8.3", "wrangler": "^4.20.0"}, "scripts": {"build": "NEXT_PRIVATE_STANDALONE=true next build", "cf-typegen": "wrangler types --env-interface CloudflareEnv env.d.ts", "check": "npm run build && tsc && wrangler deploy --dry-run", "deploy": "wrangler deploy", "dev": "next dev", "lint": "next lint", "postbuild": "opennextjs-cloudflare build -s", "preview": "npm run build && wrangler dev", "start": "next start"}}