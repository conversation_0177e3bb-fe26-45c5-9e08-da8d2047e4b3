---
description:
globs:
alwaysApply: false
---
# Configuration Files Guide

The project root contains several important configuration files:

- [package.json](mdc:package.json): Lists dependencies, scripts, and project metadata.
- [tsconfig.json](mdc:tsconfig.json): Configures TypeScript options for the project.
- [next.config.mjs](mdc:next.config.mjs): Next.js framework configuration.
- [tailwind.config.ts](mdc:tailwind.config.ts): Tailwind CSS configuration.
- [postcss.config.mjs](mdc:postcss.config.mjs): PostCSS configuration for CSS processing.
- [wrangler.json](mdc:wrangler.json): Cloudflare Workers deployment configuration.
- [.eslintrc.json](mdc:.eslintrc.json): ESLint configuration for code linting.

Refer to these files to adjust build, deployment, or development settings.
