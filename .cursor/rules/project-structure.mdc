---
description: 
globs: 
alwaysApply: false
---
# Project Structure Guide

This project is a [Next.js](mdc:README.md) application deployed on Cloudflare Workers. The main source code is located in the [src/app](mdc:src/app) directory.

## Key Files and Directories
- [package.json](mdc:package.json): Project dependencies and scripts
- [README.md](mdc:README.md): Project overview and setup instructions
- [src/app/layout.tsx](mdc:src/app/layout.tsx): Root layout for the application
- [src/app/page.tsx](mdc:src/app/page.tsx): Main page component (edit this to change the homepage)
- [src/app/globals.css](mdc:src/app/globals.css): Global CSS styles
- [tsconfig.json](mdc:tsconfig.json): TypeScript configuration
- [next.config.mjs](mdc:next.config.mjs): Next.js configuration
- [tailwind.config.ts](mdc:tailwind.config.ts): Tailwind CSS configuration
- [public/](mdc:public): Static assets

## Development
- Run the development server with `npm run dev`.
- Edit [src/app/page.tsx](mdc:src/app/page.tsx) to update the main page.

See [README.md](mdc:README.md) for more details.
