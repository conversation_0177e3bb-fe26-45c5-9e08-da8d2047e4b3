---
description:
globs:
alwaysApply: false
---
# App Directory Guide

The [src/app](mdc:src/app) directory contains the main application code for this Next.js project.

## Files
- [layout.tsx](mdc:src/app/layout.tsx): Defines the root layout for all pages, including shared components and global structure.
- [page.tsx](mdc:src/app/page.tsx): The main page component rendered at the root route (`/`).
- [globals.css](mdc:src/app/globals.css): Global CSS styles applied throughout the app.
- [favicon.ico](mdc:src/app/favicon.ico): The favicon for the site.

Edit [page.tsx](mdc:src/app/page.tsx) to change the homepage content. Use [layout.tsx](mdc:src/app/layout.tsx) to update the overall layout or add providers.
